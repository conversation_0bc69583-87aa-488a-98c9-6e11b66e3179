import { GuildMember, PermissionFlagsBits } from "discord.js";
import { GuildRoleHelper } from "../utils/guildRoleHelper";

export class AdminService {
    // Admin role names or IDs - you can configure these
    private static readonly ADMIN_ROLES = [
        'admin',
        'administrator',
        'moderator',
        'mod',
        'staff'
    ];

    // Admin user IDs - you can add specific user IDs here
    private static readonly ADMIN_USER_IDS: string[] = [
        // Add specific Discord user IDs here if needed
        // '123456789012345678'
    ];

    /**
     * Check if a user is an administrator (synchronous version for backward compatibility)
     */
    static isAdmin(member: GuildMember): boolean {
        // Check if user has administrator permission
        if (member.permissions.has(PermissionFlagsBits.Administrator)) {
            return true;
        }

        // Check if user has manage server permission
        if (member.permissions.has(PermissionFlagsBits.ManageGuild)) {
            return true;
        }

        // Check if user has manage messages permission (for moderators)
        if (member.permissions.has(PermissionFlagsBits.ManageMessages)) {
            return true;
        }

        // Check if user has any admin roles
        const hasAdminRole = member.roles.cache.some(role =>
            this.ADMIN_ROLES.some(adminRole =>
                role.name.toLowerCase().includes(adminRole.toLowerCase())
            )
        );

        if (hasAdminRole) {
            return true;
        }

        // Check if user ID is in admin list
        if (this.ADMIN_USER_IDS.includes(member.id)) {
            return true;
        }

        return false;
    }

    /**
     * Check if a user is an administrator (async version that includes configured roles)
     */
    static async isAdminAsync(member: GuildMember): Promise<boolean> {
        // First check the synchronous admin checks
        if (this.isAdmin(member)) {
            return true;
        }

        // Check configured admin roles for this guild
        const userRoleIds = member.roles.cache.map(role => role.id);
        const hasConfiguredAdminRole = await GuildRoleHelper.userHasConfiguredRole(
            member.guild.id,
            userRoleIds,
            'admin_access'
        );

        return hasConfiguredAdminRole;
    }

    /**
     * Check if a user can manage listings (async version that includes configured roles)
     */
    static async canManageListingsAsync(member: GuildMember): Promise<boolean> {
        return await this.isAdminAsync(member);
    }

    /**
     * Check if a user can manage listings (synchronous version for backward compatibility)
     */
    static canManageListings(member: GuildMember): boolean {
        return this.isAdmin(member);
    }

    /**
     * Check if a user can delete listings (async version that includes configured roles)
     */
    static async canDeleteListingsAsync(member: GuildMember): Promise<boolean> {
        // First check if user is admin (which includes configured admin roles)
        const isAdmin = await this.isAdminAsync(member);
        if (isAdmin) {
            return true;
        }

        // Also check specific Discord permissions for deletion
        return member.permissions.has(PermissionFlagsBits.Administrator) ||
               member.permissions.has(PermissionFlagsBits.ManageGuild);
    }

    /**
     * Check if a user can delete listings (synchronous version for backward compatibility)
     */
    static canDeleteListings(member: GuildMember): boolean {
        // Only administrators or users with manage server permission can delete
        return member.permissions.has(PermissionFlagsBits.Administrator) ||
               member.permissions.has(PermissionFlagsBits.ManageGuild);
    }

    /**
     * Get admin permissions for a user (async version that includes configured roles)
     */
    static async getAdminPermissionsAsync(member: GuildMember): Promise<{
        canManage: boolean;
        canDelete: boolean;
        isAdmin: boolean;
    }> {
        const isAdmin = await this.isAdminAsync(member);
        const canDelete = await this.canDeleteListingsAsync(member);

        return {
            canManage: isAdmin,
            canDelete: canDelete,
            isAdmin: isAdmin
        };
    }

    /**
     * Get admin permissions for a user (synchronous version for backward compatibility)
     */
    static getAdminPermissions(member: GuildMember): {
        canManage: boolean;
        canDelete: boolean;
        isAdmin: boolean;
    } {
        return {
            canManage: this.canManageListings(member),
            canDelete: this.canDeleteListings(member),
            isAdmin: this.isAdmin(member)
        };
    }
}
