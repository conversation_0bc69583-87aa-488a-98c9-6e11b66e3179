import { ModalSubmitInteraction, GuildMember, EmbedBuilder } from "discord.js";
import { AdminService } from "../../services/admin-service";
import { BrandingManager } from "../../services/branding-manager";

/**
 * Handle image modal submissions for corner and main images
 */
export async function handleImageModal(
    interaction: ModalSubmitInteraction,
    modalType: 'corner' | 'main',
    listingId: string
): Promise<void> {
    try {
        await interaction.deferReply({ ephemeral: true });

        // Check admin permissions
        if (!interaction.member || !interaction.guild) {
            await interaction.followUp({
                content: 'This command can only be used in a server.',
                ephemeral: true
            });
            return;
        }

        const member = interaction.member;
        if (!(member instanceof GuildMember)) {
            await interaction.followUp({
                content: 'Unable to verify permissions.',
                ephemeral: true
            });
            return;
        }

        // Use async version to check configured admin roles
        const adminPerms = await AdminService.getAdminPermissionsAsync(member);
        if (!adminPerms.isAdmin) {
            await interaction.followUp({
                content: '❌ You do not have permission to manage listings.',
                ephemeral: true
            });
            return;
        }

        // Get the image URL from the modal
        const imageUrl = interaction.fields.getTextInputValue('image_url');

        // Validate the URL
        if (!isValidImageUrl(imageUrl)) {
            await interaction.followUp({
                content: '❌ Invalid image URL. Please provide a valid HTTP/HTTPS URL ending with a common image extension (.png, .jpg, .jpeg, .gif, .webp).',
                ephemeral: true
            });
            return;
        }

        // Verify the image is accessible
        const isAccessible = await verifyImageAccessible(imageUrl);
        if (!isAccessible) {
            await interaction.followUp({
                content: '❌ Unable to access the image at the provided URL. Please check that the URL is correct and the image is publicly accessible.',
                ephemeral: true
            });
            return;
        }

        // Find and update the original listing message directly
        try {
            const channel = interaction.channel;
            if (!channel || !('messages' in channel)) {
                await interaction.followUp({
                    content: '❌ Cannot update embed in this channel type.',
                    ephemeral: true
                });
                return;
            }

            // Search more thoroughly for the listing message
            let listingMessage = null;
            let searchedCount = 0;
            const maxSearch = 200; // Search up to 200 messages
            let lastMessageId: string | undefined;

            while (!listingMessage && searchedCount < maxSearch) {
                const messages = await channel.messages.fetch({
                    limit: 50,
                    before: lastMessageId
                });

                if (messages.size === 0) break;

                listingMessage = messages.find(msg => {
                    if (msg.author.id !== interaction.client.user?.id || msg.embeds.length === 0) {
                        return false;
                    }

                    const embed = msg.embeds[0];
                    if (!embed || !embed.title) {
                        return false;
                    }

                    // Check multiple possible title formats
                    const titleChecks = [
                        embed.title.includes(`#${listingId}`),
                        embed.title.includes(`Listing #${listingId}`),
                        embed.title.includes(`Listing ${listingId}`),
                        embed.title.endsWith(`#${listingId}`)
                    ];

                    // Also check if the message has the manage button for this listing
                    const hasManageButton = msg.components?.some((row: any) =>
                        row.components?.some((component: any) =>
                            component.customId === `admin_manage_${listingId}`
                        )
                    );

                    return titleChecks.some(check => check) || hasManageButton;
                });

                searchedCount += messages.size;
                lastMessageId = messages.last()?.id;
            }

            if (!listingMessage || !listingMessage.editable) {
                console.log(`Could not find listing message for #${listingId}. Searched ${searchedCount} messages.`);
                await interaction.followUp({
                    content: `❌ Could not find the listing message for #${listingId} to update. Searched ${searchedCount} recent messages in this channel.`,
                    ephemeral: true
                });
                return;
            }

            console.log(`Found listing message for #${listingId}: ${listingMessage.id}`);

            // Get the current embed and rebuild it completely to avoid Discord attachment conflicts
            const currentEmbed = listingMessage.embeds[0];
            const embedData = currentEmbed.toJSON();
            const cleanEmbed = new EmbedBuilder()
                .setTitle(embedData.title || '')
                .setDescription(embedData.description || '')
                .setColor(embedData.color || null);

            // Add all fields back
            if (embedData.fields) {
                cleanEmbed.addFields(embedData.fields);
            }

            if (modalType === 'main') {
                // Set main image and preserve thumbnail unless it's the same URL
                cleanEmbed.setImage(imageUrl);
                if (currentEmbed.thumbnail?.url && currentEmbed.thumbnail.url !== imageUrl) {
                    cleanEmbed.setThumbnail(currentEmbed.thumbnail.url);
                }

                await listingMessage.edit({
                    embeds: [cleanEmbed],
                    components: listingMessage.components,
                    files: [], // Always clear file attachments
                    attachments: [] // Also clear attachments array
                });

                await interaction.followUp({
                    content: `✅ Main image updated locally for listing #${listingId}!`,
                    ephemeral: true
                });
            } else {
                // Set corner thumbnail and preserve main image unless it's the same URL
                cleanEmbed.setThumbnail(imageUrl);
                if (currentEmbed.image?.url && currentEmbed.image.url !== imageUrl) {
                    cleanEmbed.setImage(currentEmbed.image.url);
                }

                await listingMessage.edit({
                    embeds: [cleanEmbed],
                    components: listingMessage.components,
                    files: [], // Always clear file attachments
                    attachments: [] // Also clear attachments array
                });

                await interaction.followUp({
                    content: `✅ Corner image updated locally for listing #${listingId}!`,
                    ephemeral: true
                });
            }

        } catch (error) {
            console.error('Error updating listing message:', error);
            await interaction.followUp({
                content: '❌ Failed to update the listing embed. The message may no longer exist or be editable.',
                ephemeral: true
            });
        }

    } catch (error) {
        console.error('Error handling image modal:', error);
        await interaction.followUp({
            content: 'An error occurred while updating the image.',
            ephemeral: true
        });
    }
}

/**
 * Validate if the URL is a valid image URL
 */
function isValidImageUrl(url: string): boolean {
    try {
        const parsedUrl = new URL(url);

        // Check if it's HTTP or HTTPS
        if (!['http:', 'https:'].includes(parsedUrl.protocol)) {
            return false;
        }

        // Check if it ends with a common image extension
        const imageExtensions = ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.bmp', '.svg'];
        const pathname = parsedUrl.pathname.toLowerCase();

        return imageExtensions.some(ext => pathname.endsWith(ext));
    } catch {
        return false;
    }
}

/**
 * Verify that the image is accessible by making a HEAD request
 */
async function verifyImageAccessible(url: string): Promise<boolean> {
    try {
        const response = await fetch(url, {
            method: 'HEAD',
            headers: {
                'User-Agent': 'Discord Bot Image Validator'
            }
        });

        // Check if the response is successful and content type is an image
        if (response.ok) {
            const contentType = response.headers.get('content-type');
            return contentType ? contentType.startsWith('image/') : true;
        }

        return false;
    } catch (error) {
        console.error('Error verifying image accessibility:', error);
        return false;
    }
}

/**
 * Handle custom footer text modal submission
 */
export async function handleCustomFooterModal(
    interaction: ModalSubmitInteraction,
    listingId: string
): Promise<void> {
    try {
        await interaction.deferReply({ ephemeral: true });

        // Check admin permissions
        if (!interaction.member || !interaction.guild) {
            await interaction.followUp({
                content: 'This command can only be used in a server.',
                ephemeral: true
            });
            return;
        }

        const member = interaction.member;
        if (!(member instanceof GuildMember)) {
            await interaction.followUp({
                content: 'Unable to verify permissions.',
                ephemeral: true
            });
            return;
        }

        // Use async version to check configured admin roles
        const adminPerms = await AdminService.getAdminPermissionsAsync(member);
        if (!adminPerms.isAdmin) {
            await interaction.followUp({
                content: '❌ You do not have permission to manage listings.',
                ephemeral: true
            });
            return;
        }

        // Get the footer text from the modal
        const footerText = interaction.fields.getTextInputValue('footer_text');

        // Process line breaks (convert \\n to actual line breaks)
        const processedText = footerText.replace(/\\n/g, '\n');

        // Find the listing message to get message ID
        const channel = interaction.channel;
        if (!channel || !('messages' in channel)) {
            await interaction.followUp({
                content: '❌ Cannot update embed in this channel type.',
                ephemeral: true
            });
            return;
        }

        const guildId = interaction.guild?.id || '';
        const channelId = interaction.channel?.id || '';

        // Find the listing message
        const messages = await channel.messages.fetch({ limit: 50 });
        const listingMessage = messages.find(msg => {
            if (msg.author.id !== interaction.client.user?.id || msg.embeds.length === 0) {
                return false;
            }
            const embed = msg.embeds[0];
            return embed?.title?.includes(`#${listingId}`) ||
                   msg.components?.some((row: any) =>
                       row.components?.some((component: any) =>
                           component.customId === `admin_manage_${listingId}`
                       )
                   );
        });

        if (!listingMessage) {
            await interaction.followUp({
                content: '❌ Could not find the listing message to update.',
                ephemeral: true
            });
            return;
        }

        // Set custom footer text for this specific message
        BrandingManager.setCustomFooterText(guildId, channelId, listingMessage.id, processedText);

        // Update the embed with the new footer text
        const originalEmbed = listingMessage.embeds[0];
        const updatedEmbed = new EmbedBuilder()
            .setTitle(originalEmbed.title)
            .setColor(originalEmbed.color)
            .setDescription(originalEmbed.description)
            .setURL(originalEmbed.url);

        // Preserve images
        if (originalEmbed.image?.url) {
            updatedEmbed.setImage(originalEmbed.image.url);
        }
        if (originalEmbed.thumbnail?.url) {
            updatedEmbed.setThumbnail(originalEmbed.thumbnail.url);
        }

        // Preserve all fields
        if (originalEmbed.fields && originalEmbed.fields.length > 0) {
            updatedEmbed.addFields(...originalEmbed.fields);
        }

        // Apply custom footer text
        const customLogoUrl = BrandingManager.getCustomLogoUrl(guildId, channelId, listingMessage.id);
        const logoUrl = customLogoUrl || originalEmbed.footer?.iconURL || 'https://r2.wikily.gg/images/brand/wikilyLogo_300.png';

        updatedEmbed.setFooter({
            text: processedText,
            iconURL: logoUrl
        });

        // Handle attachment regeneration if needed
        const editOptions: any = {
            embeds: [updatedEmbed],
            components: listingMessage.components
        };

        // Check if the embed uses attachment URLs
        const hasAttachmentImage = originalEmbed.image?.url?.startsWith('attachment://');
        const hasAttachmentThumbnail = originalEmbed.thumbnail?.url?.startsWith('attachment://');

        if (hasAttachmentImage || hasAttachmentThumbnail) {
            // Need to regenerate the attachment for the listing
            const { ImageService } = await import('../../services/image-service.js');
            const regeneratedAttachment = await ImageService.createListingPreviewImage(parseInt(listingId));

            if (regeneratedAttachment) {
                editOptions.files = [regeneratedAttachment];
            } else {
                // If regeneration fails, clear the attachment URL and use no image
                if (hasAttachmentImage) {
                    updatedEmbed.setImage(null);
                }
                if (hasAttachmentThumbnail) {
                    updatedEmbed.setThumbnail(null);
                }
                editOptions.files = [];
            }
        } else {
            editOptions.files = [];
            editOptions.attachments = [];
        }

        await listingMessage.edit(editOptions);

        await interaction.followUp({
            content: `✅ Custom footer text set for listing #${listingId}!`,
            ephemeral: true
        });

    } catch (error) {
        console.error('Error handling custom footer modal:', error);
        await interaction.followUp({
            content: 'An error occurred while setting the custom footer text.',
            ephemeral: true
        });
    }
}

/**
 * Handle custom logo URL modal submission
 */
export async function handleCustomLogoModal(
    interaction: ModalSubmitInteraction,
    listingId: string
): Promise<void> {
    try {
        await interaction.deferReply({ ephemeral: true });

        // Check admin permissions
        if (!interaction.member || !interaction.guild) {
            await interaction.followUp({
                content: 'This command can only be used in a server.',
                ephemeral: true
            });
            return;
        }

        const member = interaction.member;
        if (!(member instanceof GuildMember)) {
            await interaction.followUp({
                content: 'Unable to verify permissions.',
                ephemeral: true
            });
            return;
        }

        // Use async version to check configured admin roles
        const adminPerms = await AdminService.getAdminPermissionsAsync(member);
        if (!adminPerms.isAdmin) {
            await interaction.followUp({
                content: '❌ You do not have permission to manage listings.',
                ephemeral: true
            });
            return;
        }

        // Get the logo URL from the modal
        const logoUrl = interaction.fields.getTextInputValue('logo_url');

        // Validate the URL
        if (!isValidImageUrl(logoUrl)) {
            await interaction.followUp({
                content: '❌ Invalid image URL. Please provide a valid HTTP/HTTPS URL ending with a common image extension (.png, .jpg, .jpeg, .gif, .webp).',
                ephemeral: true
            });
            return;
        }

        // Verify the image is accessible
        const isAccessible = await verifyImageAccessible(logoUrl);
        if (!isAccessible) {
            await interaction.followUp({
                content: '❌ Unable to access the image at the provided URL. Please check that the URL is correct and the image is publicly accessible.',
                ephemeral: true
            });
            return;
        }

        // Find the listing message to get message ID
        const channel = interaction.channel;
        if (!channel || !('messages' in channel)) {
            await interaction.followUp({
                content: '❌ Cannot update embed in this channel type.',
                ephemeral: true
            });
            return;
        }

        const guildId = interaction.guild?.id || '';
        const channelId = interaction.channel?.id || '';

        // Find the listing message
        const messages = await channel.messages.fetch({ limit: 50 });
        const listingMessage = messages.find(msg => {
            if (msg.author.id !== interaction.client.user?.id || msg.embeds.length === 0) {
                return false;
            }
            const embed = msg.embeds[0];
            return embed?.title?.includes(`#${listingId}`) ||
                   msg.components?.some((row: any) =>
                       row.components?.some((component: any) =>
                           component.customId === `admin_manage_${listingId}`
                       )
                   );
        });

        if (!listingMessage) {
            await interaction.followUp({
                content: '❌ Could not find the listing message to update.',
                ephemeral: true
            });
            return;
        }

        // Set custom logo URL for this specific message
        BrandingManager.setCustomLogoUrl(guildId, channelId, listingMessage.id, logoUrl);

        // Update the embed with the new logo
        const originalEmbed = listingMessage.embeds[0];
        const updatedEmbed = new EmbedBuilder()
            .setTitle(originalEmbed.title)
            .setColor(originalEmbed.color)
            .setDescription(originalEmbed.description)
            .setURL(originalEmbed.url);

        // Preserve images
        if (originalEmbed.image?.url) {
            updatedEmbed.setImage(originalEmbed.image.url);
        }
        if (originalEmbed.thumbnail?.url) {
            updatedEmbed.setThumbnail(originalEmbed.thumbnail.url);
        }

        // Preserve all fields
        if (originalEmbed.fields && originalEmbed.fields.length > 0) {
            updatedEmbed.addFields(...originalEmbed.fields);
        }

        // Apply custom logo URL
        const customFooterText = BrandingManager.getCustomFooterText(guildId, channelId, listingMessage.id);
        const footerText = customFooterText || originalEmbed.footer?.text || `Powered by\nWikily`;

        updatedEmbed.setFooter({
            text: footerText,
            iconURL: logoUrl
        });

        // Handle attachment regeneration if needed
        const editOptions: any = {
            embeds: [updatedEmbed],
            components: listingMessage.components
        };

        // Check if the embed uses attachment URLs
        const hasAttachmentImage = originalEmbed.image?.url?.startsWith('attachment://');
        const hasAttachmentThumbnail = originalEmbed.thumbnail?.url?.startsWith('attachment://');

        if (hasAttachmentImage || hasAttachmentThumbnail) {
            // Need to regenerate the attachment for the listing
            const { ImageService } = await import('../../services/image-service.js');
            const regeneratedAttachment = await ImageService.createListingPreviewImage(parseInt(listingId));

            if (regeneratedAttachment) {
                editOptions.files = [regeneratedAttachment];
            } else {
                // If regeneration fails, clear the attachment URL and use no image
                if (hasAttachmentImage) {
                    updatedEmbed.setImage(null);
                }
                if (hasAttachmentThumbnail) {
                    updatedEmbed.setThumbnail(null);
                }
                editOptions.files = [];
            }
        } else {
            editOptions.files = [];
            editOptions.attachments = [];
        }

        await listingMessage.edit(editOptions);

        await interaction.followUp({
            content: `✅ Custom logo URL set for listing #${listingId}!`,
            ephemeral: true
        });

    } catch (error) {
        console.error('Error handling custom logo modal:', error);
        await interaction.followUp({
            content: 'An error occurred while setting the custom logo URL.',
            ephemeral: true
        });
    }
}


