import { ButtonInteraction, GuildMember } from "discord.js";
import { AdminService } from "../../../services/admin-service";
import { EmbedService } from "../../../services/embed-service";
import { AdminMenuBuilder } from "./admin-menu-builder";
import pool from "../../../utils/dbConfig";

/**
 * Handles core admin actions like delete and refresh
 */
export class AdminActions {
    /**
     * Handle listing deletion request
     */
    static async handleDeleteListing(
        interaction: ButtonInteraction,
        listingId?: string,
        adminPerms?: any
    ): Promise<void> {
        if (!listingId) {
            await interaction.reply({
                content: 'Invalid listing ID.',
                ephemeral: true
            });
            return;
        }

        if (!adminPerms?.canDelete) {
            await interaction.reply({
                content: '❌ You do not have permission to delete listings.',
                ephemeral: true
            });
            return;
        }

        try {
            await interaction.deferUpdate();

            const confirmButtons = AdminMenuBuilder.buildDeleteConfirmationButtons(listingId);

            await interaction.followUp({
                content: `⚠️ **Confirm Deletion**\nAre you sure you want to delete listing #${listingId}?\n\n**This action cannot be undone!**`,
                components: [confirmButtons],
                ephemeral: true
            });

        } catch (error) {
            console.error('Error handling delete listing:', error);
            await interaction.followUp({
                content: 'An error occurred while processing the delete request.',
                ephemeral: true
            });
        }
    }

    /**
     * Handle delete confirmation
     */
    static async handleDeleteConfirmation(
        interaction: ButtonInteraction,
        action: string,
        listingId: string
    ): Promise<void> {
        // Check admin permissions again
        if (!interaction.member || !interaction.guild) {
            await interaction.reply({
                content: 'This command can only be used in a server.',
                ephemeral: true
            });
            return;
        }

        const member = interaction.member;
        if (!(member instanceof GuildMember)) {
            await interaction.reply({
                content: 'Unable to verify permissions.',
                ephemeral: true
            });
            return;
        }

        // Use async version to check configured admin roles
        const adminPerms = await AdminService.getAdminPermissionsAsync(member);

        if (!adminPerms.canDelete) {
            await interaction.reply({
                content: '❌ You do not have permission to delete listings.',
                ephemeral: true
            });
            return;
        }

        try {
            await interaction.deferUpdate();

            if (action === 'confirm') {
                await this.performDeletion(interaction, listingId);
            } else {
                await interaction.followUp({
                    content: '❌ Deletion cancelled.',
                    ephemeral: true
                });
            }

        } catch (error) {
            console.error('Error handling delete confirmation:', error);
            await interaction.followUp({
                content: 'An error occurred while processing the deletion.',
                ephemeral: true
            });
        }
    }

    /**
     * Perform the actual deletion
     */
    private static async performDeletion(interaction: ButtonInteraction, listingId: string): Promise<void> {
        const deleteQuery = `
            UPDATE asa.listings
            SET status = 'deleted', last_updated = NOW()
            WHERE listing_id = $1
        `;

        const result = await pool.query(deleteQuery, [listingId]);

        if (result.rowCount && result.rowCount > 0) {
            await interaction.followUp({
                content: `✅ Listing #${listingId} has been deleted successfully.`,
                ephemeral: true
            });

            await this.updateOriginalMessageAfterDeletion(interaction, listingId);
        } else {
            await interaction.followUp({
                content: `❌ Failed to delete listing #${listingId}. It may not exist.`,
                ephemeral: true
            });
        }
    }

    /**
     * Update the original message after deletion
     */
    private static async updateOriginalMessageAfterDeletion(
        interaction: ButtonInteraction,
        listingId: string
    ): Promise<void> {
        try {
            const channel = interaction.channel;
            if (channel && 'messages' in channel) {
                const messages = await channel.messages.fetch({ limit: 50 });

                const originalMessage = messages.find(msg => {
                    if (msg.author.id !== interaction.client.user?.id) {
                        return false;
                    }

                    if (msg.components && msg.components.length > 0) {
                        const hasManageButton = msg.components.some((row: any) =>
                            row.components && row.components.some((component: any) =>
                                component.customId === `admin_manage_${listingId}`
                            )
                        );
                        if (hasManageButton) {
                            return true;
                        }
                    }

                    return false;
                });

                if (originalMessage && originalMessage.editable) {
                    await originalMessage.edit({
                        content: `🗑️ **Listing Deleted**\nListing #${listingId} has been removed.`,
                        embeds: [],
                        components: []
                    });
                }
            }
        } catch (error) {
            console.error('Error updating original message after deletion:', error);
        }
    }

    /**
     * Handle listing refresh
     */
    static async handleRefreshListing(
        interaction: ButtonInteraction,
        listingId?: string
    ): Promise<void> {
        if (!listingId) {
            await interaction.reply({
                content: 'Invalid listing ID.',
                ephemeral: true
            });
            return;
        }

        try {
            await interaction.deferUpdate();

            const listing = await this.fetchListingData(listingId);
            if (!listing) {
                await interaction.followUp({
                    content: '❌ Listing not found.',
                    ephemeral: true
                });
                return;
            }

            await this.updateOriginalMessageWithRefreshedData(interaction, listing, listingId);

            await interaction.followUp({
                content: '✅ Listing data refreshed successfully!',
                ephemeral: true
            });

        } catch (error) {
            console.error('Error refreshing listing:', error);
            await interaction.followUp({
                content: 'An error occurred while refreshing the listing.',
                ephemeral: true
            });
        }
    }

    /**
     * Fetch listing data from database
     */
    private static async fetchListingData(listingId: string): Promise<any> {
        const listingQuery = `
            SELECT l.*
            FROM asa.listings l
            WHERE l.listing_id = $1
        `;

        const listingResult = await pool.query(listingQuery, [listingId]);
        return listingResult.rows.length > 0 ? listingResult.rows[0] : null;
    }

    /**
     * Update original message with refreshed data
     */
    private static async updateOriginalMessageWithRefreshedData(
        interaction: ButtonInteraction,
        listing: any,
        listingId: string
    ): Promise<void> {
        const { embed: refreshedEmbed, attachment } = await EmbedService.createListingEmbed(listing);

        try {
            const channel = interaction.channel;
            if (channel && 'messages' in channel) {
                const messages = await channel.messages.fetch({ limit: 20 });

                const originalMessage = messages.find(msg => {
                    if (msg.author.id !== interaction.client.user?.id || msg.embeds.length === 0) {
                        return false;
                    }

                    const embed = msg.embeds[0];
                    if (!embed || !embed.title) {
                        return false;
                    }

                    return embed.title.includes(`#${listingId}`) ||
                           embed.title.includes(`Listing #${listingId}`) ||
                           (embed.title.includes('Listing') && embed.fields?.some(field =>
                               field.value?.includes(`#${listingId}`)
                           ));
                });

                if (originalMessage && originalMessage.editable) {
                    console.log(`Found original message to refresh for listing ${listingId}`);

                    const updateOptions: any = {
                        embeds: [refreshedEmbed],
                        components: originalMessage.components
                    };

                    if (attachment) {
                        updateOptions.files = [attachment];
                    }

                    await originalMessage.edit(updateOptions);
                    console.log(`Successfully refreshed original message for listing ${listingId}`);
                } else {
                    console.log(`Could not find editable original message for listing ${listingId}`);
                }
            }
        } catch (error) {
            console.error('Error finding/updating original message:', error);
        }
    }
}
