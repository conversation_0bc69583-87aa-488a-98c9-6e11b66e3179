import { ButtonInteraction, GuildMember } from "discord.js";
import { AdminService } from "../../services/admin-service";
import { AdminMenuBuilder } from "./admin/admin-menu-builder";
import { AdminActions } from "./admin/admin-actions";
import { AdminModalHandlers } from "./admin/admin-modal-handlers";
import { AdminToggleHandlers } from "./admin/admin-toggle-handlers";
import { AdminHelpers } from "./admin/admin-helpers";

/**
 * Handle admin-related button actions
 */
export async function handleAdminButton(
    interaction: ButtonInteraction,
    subAction: string,
    listingId?: string
): Promise<void> {
    // Check if user is an admin
    if (!interaction.member || !interaction.guild) {
        await interaction.reply({
            content: 'This command can only be used in a server.',
            ephemeral: true
        });
        return;
    }

    const member = interaction.member;
    if (!(member instanceof GuildMember)) {
        await interaction.reply({
            content: 'Unable to verify permissions.',
            ephemeral: true
        });
        return;
    }

    // Use async version to check configured admin roles
    const adminPerms = await AdminService.getAdminPermissionsAsync(member);

    if (!adminPerms.isAdmin) {
        await interaction.reply({
            content: '❌ You do not have permission to manage listings.',
            ephemeral: true
        });
        return;
    }

    switch (subAction) {
        case 'manage':
            await AdminMenuBuilder.createManagementMenu(interaction, listingId || '', adminPerms);
            break;
        case 'delete':
            await AdminActions.handleDeleteListing(interaction, listingId, adminPerms);
            break;
        case 'refresh':
            await AdminActions.handleRefreshListing(interaction, listingId);
            break;
        case 'corner':
            // Handle corner image button: admin_corner_image_123
            const cornerListingId = AdminHelpers.extractListingIdFromCustomId(interaction.customId);
            await AdminModalHandlers.handleSetCornerImage(interaction, cornerListingId);
            break;
        case 'main':
            // Handle main image button: admin_main_image_123
            const mainListingId = AdminHelpers.extractListingIdFromCustomId(interaction.customId);
            await AdminModalHandlers.handleSetMainImage(interaction, mainListingId);
            break;
        case 'branding':
            await AdminToggleHandlers.handleToggleBranding(interaction, listingId);
            break;
        case 'link':
            await AdminToggleHandlers.handleToggleLink(interaction, listingId);
            break;
        case 'cluster':
            // Handle cluster text toggle: admin_cluster_text_123
            const clusterListingId = AdminHelpers.extractListingIdFromCustomId(interaction.customId);
            await AdminToggleHandlers.handleClusterTextToggle(interaction, clusterListingId);
            break;
        case 'type':
            // Handle type text toggle: admin_type_text_123
            const typeListingId = AdminHelpers.extractListingIdFromCustomId(interaction.customId);
            await AdminToggleHandlers.handleTypeTextToggle(interaction, typeListingId);
            break;
        case 'category':
            // Handle category text toggle: admin_category_text_123
            const categoryListingId = AdminHelpers.extractListingIdFromCustomId(interaction.customId);
            await AdminToggleHandlers.handleCategoryTextToggle(interaction, categoryListingId);
            break;
        case 'description':
            // Handle description text toggle: admin_description_text_123
            const descriptionListingId = AdminHelpers.extractListingIdFromCustomId(interaction.customId);
            await AdminToggleHandlers.handleDescriptionTextToggle(interaction, descriptionListingId);
            break;
        case 'footer':
            // Handle custom footer text button: admin_footer_text_123
            const footerListingId = AdminHelpers.extractListingIdFromCustomId(interaction.customId);
            await AdminModalHandlers.handleSetCustomFooterText(interaction, footerListingId);
            break;
        case 'logo':
            // Handle custom logo URL button: admin_logo_url_123
            const logoListingId = AdminHelpers.extractListingIdFromCustomId(interaction.customId);
            await AdminModalHandlers.handleSetCustomLogoUrl(interaction, logoListingId);
            break;
        default:
            await interaction.reply({
                content: 'Unknown admin action.',
                ephemeral: true
            });
    }
}

/**
 * Handle delete confirmation
 */
export async function handleDeleteConfirmation(
    interaction: ButtonInteraction,
    action: string,
    listingId: string
): Promise<void> {
    return AdminActions.handleDeleteConfirmation(interaction, action, listingId);
}
