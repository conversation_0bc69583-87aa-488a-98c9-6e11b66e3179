/**
 * Comprehensive test for Toggle Link functionality including re-enable
 */

const { BrandingManager } = require('./dist/services/branding-manager.js');
const { EmbedUpdateService } = require('./dist/services/embed-update-service.js');

const mockEmbedWithUrl = {
    title: 'Test Listing #123',
    color: 0x00ff00,
    description: 'Test description',
    url: 'https://wikily.gg/ark-survival-ascended/trading/listings/123/',
    fields: [{ name: 'Price', value: '100 Metal Ingot', inline: true }]
};

const mockEmbedWithoutUrl = {
    title: 'Test Listing #123',
    color: 0x00ff00,
    description: 'Test description',
    fields: [{ name: 'Price', value: '100 Metal Ingot', inline: true }]
};

const guildId = 'test-guild';
const channelId = 'test-channel';
const messageId = 'test-message';

console.log('🧪 Comprehensive Toggle Link Test\n');

// Test 1: Start with URL, disable link
console.log('Test 1: Disable link (URL should be removed)');
BrandingManager.disableLink(guildId, channelId, messageId);
const disabledResult = EmbedUpdateService.updateEmbedWithBranding(
    mockEmbedWithUrl,
    guildId,
    channelId,
    messageId
);
console.log(`URL after disable: ${disabledResult.data.url}`);
console.log(`✅ Test 1 ${disabledResult.data.url === undefined ? 'PASSED' : 'FAILED'}\n`);

// Test 2: Re-enable link with original embed (has URL)
console.log('Test 2: Re-enable link with original embed');
BrandingManager.enableLink(guildId, channelId, messageId);
const reenabledWithOriginal = EmbedUpdateService.updateEmbedWithBranding(
    mockEmbedWithUrl,
    guildId,
    channelId,
    messageId
);
console.log(`URL after re-enable (with original): ${reenabledWithOriginal.data.url}`);
console.log(`✅ Test 2 ${reenabledWithOriginal.data.url === mockEmbedWithUrl.url ? 'PASSED' : 'FAILED'}\n`);

// Test 3: Re-enable link with embed missing URL (key test)
console.log('Test 3: Re-enable link with embed missing URL');
const reenabledWithoutOriginal = EmbedUpdateService.updateEmbedWithBranding(
    mockEmbedWithoutUrl,
    guildId,
    channelId,
    messageId
);
console.log(`URL after re-enable (without original): ${reenabledWithoutOriginal.data.url}`);
const expectedUrl = 'https://wikily.gg/ark-survival-ascended/trading/listings/123/';
console.log(`✅ Test 3 ${reenabledWithoutOriginal.data.url === expectedUrl ? 'PASSED' : 'FAILED'}\n`);

// Test 4: Verify other properties preserved
console.log('Test 4: Verify other properties preserved');
const titleOk = reenabledWithoutOriginal.data.title === mockEmbedWithoutUrl.title;
const colorOk = reenabledWithoutOriginal.data.color === mockEmbedWithoutUrl.color;
const descOk = reenabledWithoutOriginal.data.description === mockEmbedWithoutUrl.description;
const fieldsOk = reenabledWithoutOriginal.data.fields?.length === mockEmbedWithoutUrl.fields.length;
console.log(`Properties preserved: ${titleOk && colorOk && descOk && fieldsOk}`);
console.log(`✅ Test 4 ${titleOk && colorOk && descOk && fieldsOk ? 'PASSED' : 'FAILED'}\n`);

// Summary
const allTests = [
    disabledResult.data.url === undefined,
    reenabledWithOriginal.data.url === mockEmbedWithUrl.url,
    reenabledWithoutOriginal.data.url === expectedUrl,
    titleOk && colorOk && descOk && fieldsOk
];

const passedTests = allTests.filter(test => test).length;
console.log(`🎯 Summary: ${passedTests}/4 tests passed`);
console.log(`${passedTests === 4 ? '🎉 ALL TESTS PASSED!' : '❌ SOME TESTS FAILED'}`);

// Clean up
BrandingManager.clearAll();
console.log('\n🧹 Cleaned up test data');
