/**
 * Test script to verify Toggle Link re-enable functionality
 * This tests the scenario where URL is lost after disabling and needs to be reconstructed
 */

const { BrandingManager } = require('./dist/services/branding-manager.js');
const { EmbedUpdateService } = require('./dist/services/embed-update-service.js');

// Mock embed data with URL
const mockEmbedWithUrl = {
    title: 'Test Listing #123',
    color: 0x00ff00,
    description: 'Test description',
    url: 'https://wikily.gg/ark-survival-ascended/trading/listings/123/',
    fields: [
        { name: 'Price', value: '100 Metal Ingot', inline: true },
        { name: 'Type', value: '🛒 Selling', inline: true }
    ],
    footer: {
        text: 'Powered by\nWikily',
        iconURL: 'https://r2.wikily.gg/images/brand/wikilyLogo_300.png'
    }
};

// Mock embed data without URL (simulating after disable)
const mockEmbedWithoutUrl = {
    title: 'Test Listing #123',
    color: 0x00ff00,
    description: 'Test description',
    // url: undefined (missing)
    fields: [
        { name: 'Price', value: '100 Metal Ingot', inline: true },
        { name: 'Type', value: '🛒 Selling', inline: true }
    ],
    footer: {
        text: 'Powered by\nWikily',
        iconURL: 'https://r2.wikily.gg/images/brand/wikilyLogo_300.png'
    }
};

const guildId = 'test-guild';
const channelId = 'test-channel';
const messageId = 'test-message';

console.log('🧪 Testing Toggle Link Re-enable Functionality\n');

// Test 1: Normal case - embed has URL, link enabled
console.log('Test 1: Normal case - embed has URL, link enabled');
const embedWithUrl = EmbedUpdateService.updateEmbedWithBranding(
    mockEmbedWithUrl,
    guildId,
    channelId,
    messageId
);
console.log(`Embed URL: ${embedWithUrl.data.url}`);
console.log(`Expected: ${mockEmbedWithUrl.url}, Actual: ${embedWithUrl.data.url}`);
console.log(`✅ Test 1 ${embedWithUrl.data.url === mockEmbedWithUrl.url ? 'PASSED' : 'FAILED'}\n`);

// Test 2: Disable link - URL should be removed
console.log('Test 2: Disable link - URL should be removed');
BrandingManager.disableLink(guildId, channelId, messageId);
const embedWithoutUrl = EmbedUpdateService.updateEmbedWithBranding(
    mockEmbedWithUrl,
    guildId,
    channelId,
    messageId
);
console.log(`Embed URL when disabled: ${embedWithoutUrl.data.url}`);
console.log(`Expected: undefined, Actual: ${embedWithoutUrl.data.url}`);
console.log(`✅ Test 2 ${embedWithoutUrl.data.url === undefined ? 'PASSED' : 'FAILED'}\n`);

// Test 3: Re-enable link with original embed (has URL) - should work
console.log('Test 3: Re-enable link with original embed (has URL)');
BrandingManager.enableLink(guildId, channelId, messageId);
const embedReenabledWithOriginal = EmbedUpdateService.updateEmbedWithBranding(
    mockEmbedWithUrl,
    guildId,
    channelId,
    messageId
);
console.log(`Embed URL when re-enabled (original): ${embedReenabledWithOriginal.data.url}`);
console.log(`Expected: ${mockEmbedWithUrl.url}, Actual: ${embedReenabledWithOriginal.data.url}`);
console.log(`✅ Test 3 ${embedReenabledWithOriginal.data.url === mockEmbedWithUrl.url ? 'PASSED' : 'FAILED'}\n`);

// Test 4: Re-enable link with embed missing URL - should reconstruct
console.log('Test 4: Re-enable link with embed missing URL (key test)');
const embedReenabledWithoutOriginal = EmbedUpdateService.updateEmbedWithBranding(
    mockEmbedWithoutUrl,
    guildId,
    channelId,
    messageId
);
console.log(`Embed URL when re-enabled (no original): ${embedReenabledWithoutOriginal.data.url}`);
const expectedReconstructedUrl = 'https://wikily.gg/ark-survival-ascended/trading/listings/123/';
console.log(`Expected: ${expectedReconstructedUrl}, Actual: ${embedReenabledWithoutOriginal.data.url}`);
console.log(`✅ Test 4 ${embedReenabledWithoutOriginal.data.url === expectedReconstructedUrl ? 'PASSED' : 'FAILED'}\n`);

// Test 5: Disable again with embed that has no URL - should stay disabled
console.log('Test 5: Disable again with embed that has no URL');
BrandingManager.disableLink(guildId, channelId, messageId);
const embedDisabledAgain = EmbedUpdateService.updateEmbedWithBranding(
    mockEmbedWithoutUrl,
    guildId,
    channelId,
    messageId
);
console.log(`Embed URL when disabled again: ${embedDisabledAgain.data.url}`);
console.log(`Expected: undefined, Actual: ${embedDisabledAgain.data.url}`);
console.log(`✅ Test 5 ${embedDisabledAgain.data.url === undefined ? 'PASSED' : 'FAILED'}\n`);

// Test 6: Test with different listing ID format in title
console.log('Test 6: Test with different listing ID format');
const mockEmbedDifferentFormat = {
    title: 'Listing #456 - Special Item',
    color: 0x00ff00,
    description: 'Test description'
};

BrandingManager.enableLink(guildId, channelId, messageId);
const embedDifferentFormat = EmbedUpdateService.updateEmbedWithBranding(
    mockEmbedDifferentFormat,
    guildId,
    channelId,
    messageId
);
console.log(`Embed URL with different format: ${embedDifferentFormat.data.url}`);
const expectedDifferentUrl = 'https://wikily.gg/ark-survival-ascended/trading/listings/456/';
console.log(`Expected: ${expectedDifferentUrl}, Actual: ${embedDifferentFormat.data.url}`);
console.log(`✅ Test 6 ${embedDifferentFormat.data.url === expectedDifferentUrl ? 'PASSED' : 'FAILED'}\n`);

// Summary
console.log('🎯 Test Summary:');
const allTests = [
    embedWithUrl.data.url === mockEmbedWithUrl.url,
    embedWithoutUrl.data.url === undefined,
    embedReenabledWithOriginal.data.url === mockEmbedWithUrl.url,
    embedReenabledWithoutOriginal.data.url === expectedReconstructedUrl,
    embedDisabledAgain.data.url === undefined,
    embedDifferentFormat.data.url === expectedDifferentUrl
];

const passedTests = allTests.filter(test => test).length;
const totalTests = allTests.length;

console.log(`Passed: ${passedTests}/${totalTests}`);
console.log(`${passedTests === totalTests ? '🎉 ALL TESTS PASSED!' : '❌ SOME TESTS FAILED'}`);

// Clean up
BrandingManager.clearAll();
console.log('\n🧹 Cleaned up test data');
