/**
 * Test script to verify Toggle Link functionality
 * This script tests the BrandingManager and EmbedUpdateService
 */

const { BrandingManager } = require('./dist/services/branding-manager.js');
const { EmbedUpdateService } = require('./dist/services/embed-update-service.js');

// Mock embed data
const mockEmbed = {
    title: 'Test Listing #123',
    color: 0x00ff00,
    description: 'Test description',
    url: 'https://wikily.gg/ark-survival-ascended/trading/listings/123/',
    fields: [
        { name: 'Price', value: '100 Metal Ingot', inline: true },
        { name: 'Type', value: '🛒 Selling', inline: true }
    ],
    footer: {
        text: 'Powered by\nWikily',
        iconURL: 'https://r2.wikily.gg/images/brand/wikilyLogo_300.png'
    }
};

const guildId = 'test-guild';
const channelId = 'test-channel';
const messageId = 'test-message';

console.log('🧪 Testing Toggle Link Functionality\n');

// Test 1: Initial state (link should be enabled by default)
console.log('Test 1: Initial state');
const isInitiallyDisabled = BrandingManager.isLinkDisabled(guildId, channelId, messageId);
console.log(`Link disabled initially: ${isInitiallyDisabled}`);
console.log(`Expected: false, Actual: ${isInitiallyDisabled}`);
console.log(`✅ Test 1 ${!isInitiallyDisabled ? 'PASSED' : 'FAILED'}\n`);

// Test 2: Disable link
console.log('Test 2: Disable link');
BrandingManager.disableLink(guildId, channelId, messageId);
const isDisabledAfterToggle = BrandingManager.isLinkDisabled(guildId, channelId, messageId);
console.log(`Link disabled after toggle: ${isDisabledAfterToggle}`);
console.log(`Expected: true, Actual: ${isDisabledAfterToggle}`);
console.log(`✅ Test 2 ${isDisabledAfterToggle ? 'PASSED' : 'FAILED'}\n`);

// Test 3: Enable link
console.log('Test 3: Enable link');
BrandingManager.enableLink(guildId, channelId, messageId);
const isEnabledAfterToggle = BrandingManager.isLinkDisabled(guildId, channelId, messageId);
console.log(`Link disabled after re-enable: ${isEnabledAfterToggle}`);
console.log(`Expected: false, Actual: ${isEnabledAfterToggle}`);
console.log(`✅ Test 3 ${!isEnabledAfterToggle ? 'PASSED' : 'FAILED'}\n`);

// Test 4: Embed update with link enabled
console.log('Test 4: Embed update with link enabled');
const embedWithLink = EmbedUpdateService.updateEmbedWithBranding(
    mockEmbed,
    guildId,
    channelId,
    messageId
);
console.log(`Embed URL when link enabled: ${embedWithLink.data.url}`);
console.log(`Expected: ${mockEmbed.url}, Actual: ${embedWithLink.data.url}`);
console.log(`✅ Test 4 ${embedWithLink.data.url === mockEmbed.url ? 'PASSED' : 'FAILED'}\n`);

// Test 5: Embed update with link disabled
console.log('Test 5: Embed update with link disabled');
BrandingManager.disableLink(guildId, channelId, messageId);
const embedWithoutLink = EmbedUpdateService.updateEmbedWithBranding(
    mockEmbed,
    guildId,
    channelId,
    messageId
);
console.log(`Embed URL when link disabled: ${embedWithoutLink.data.url}`);
console.log(`Expected: undefined, Actual: ${embedWithoutLink.data.url}`);
console.log(`✅ Test 5 ${embedWithoutLink.data.url === undefined ? 'PASSED' : 'FAILED'}\n`);

// Test 6: Verify other embed properties are preserved
console.log('Test 6: Verify other properties are preserved');
const titlePreserved = embedWithoutLink.data.title === mockEmbed.title;
const colorPreserved = embedWithoutLink.data.color === mockEmbed.color;
const descriptionPreserved = embedWithoutLink.data.description === mockEmbed.description;
const fieldsPreserved = embedWithoutLink.data.fields && embedWithoutLink.data.fields.length === mockEmbed.fields.length;

console.log(`Title preserved: ${titlePreserved}`);
console.log(`Color preserved: ${colorPreserved}`);
console.log(`Description preserved: ${descriptionPreserved}`);
console.log(`Fields preserved: ${fieldsPreserved}`);

const allPreserved = titlePreserved && colorPreserved && descriptionPreserved && fieldsPreserved;
console.log(`✅ Test 6 ${allPreserved ? 'PASSED' : 'FAILED'}\n`);

// Summary
console.log('🎯 Test Summary:');
const allTests = [
    !isInitiallyDisabled,
    isDisabledAfterToggle,
    !isEnabledAfterToggle,
    embedWithLink.data.url === mockEmbed.url,
    embedWithoutLink.data.url === undefined,
    allPreserved
];

const passedTests = allTests.filter(test => test).length;
const totalTests = allTests.length;

console.log(`Passed: ${passedTests}/${totalTests}`);
console.log(`${passedTests === totalTests ? '🎉 ALL TESTS PASSED!' : '❌ SOME TESTS FAILED'}`);

// Clean up
BrandingManager.clearAll();
console.log('\n🧹 Cleaned up test data');
