/**
 * Debug script to test extractListingId method
 */

const { EmbedUpdateService } = require('./dist/services/embed-update-service.js');

// Test embeds
const testEmbeds = [
    {
        title: 'Test Listing #123',
        description: 'Test description'
    },
    {
        title: 'Listing #456 - Special Item',
        description: 'Test description'
    },
    {
        title: 'Some Item Listing #789',
        description: 'Test description'
    },
    {
        title: 'Test Listing #123',
        url: 'https://wikily.gg/ark-survival-ascended/trading/listings/123/',
        description: 'Test description'
    },
    {
        title: 'No ID here',
        url: 'https://wikily.gg/ark-survival-ascended/trading/listings/999/',
        description: 'Test description'
    }
];

console.log('🔍 Testing extractListingId method\n');

// We need to access the private method, so let's create a test version
function extractListingId(originalEmbed) {
    let listingId = null;
    if (originalEmbed.title) {
        const titleMatch = originalEmbed.title.match(/#(\d+)/);
        if (titleMatch) {
            listingId = parseInt(titleMatch[1]);
        }
    }
    if (!listingId && originalEmbed.url) {
        const urlMatch = originalEmbed.url.match(/\/listings\/(\d+)\//);
        if (urlMatch) {
            listingId = parseInt(urlMatch[1]);
        }
    }
    return listingId;
}

testEmbeds.forEach((embed, index) => {
    console.log(`Test ${index + 1}:`);
    console.log(`Title: "${embed.title}"`);
    console.log(`URL: ${embed.url || 'undefined'}`);
    
    const extractedId = extractListingId(embed);
    console.log(`Extracted ID: ${extractedId}`);
    
    if (extractedId) {
        const reconstructedUrl = `https://wikily.gg/ark-survival-ascended/trading/listings/${extractedId}/`;
        console.log(`Reconstructed URL: ${reconstructedUrl}`);
    }
    
    console.log('---\n');
});
